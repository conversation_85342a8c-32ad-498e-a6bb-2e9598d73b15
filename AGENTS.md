# Repository Guidelines
- 使用中文回答问题
- 使用mcp协助思考，解决问题
## Project Structure & Module Organization
- `app/` hosts FastAPI source split by domain (`api/`, `services/`, `crud/`, `schemas/`, `tasks/`). Entry point is `app/main.py`; Celery setup lives in `app/core/`.
- `tests/` mirrors the app layout; create new suites under matching subdirectories (e.g., `tests/services/test_new_service.py`).
- `alembic/` tracks database migrations; new revisions should live in `alembic/versions/`.
- `scripts/`, `run_worker.sh`, and `run_beat.sh` contain operational helpers; keep any new automation utilities here.
- `static/` and `docs/` store shared assets and documentation. Avoid mixing runtime artifacts into these folders.

## Build, Test, and Development Commands
- `uv pip install -r pyproject.toml` installs runtime and dev dependencies using `uv` (preferred). `pip install -e .[dev]` is acceptable when `uv` is unavailable.
- `uvicorn app.main:app --reload` starts the API locally.
- `pytest` executes the full test suite; focus runs can target a module, e.g., `pytest tests/api/test_user.py::test_create_user`.
- `ruff check app tests` performs linting, while `ruff format .` applies project formatting defaults.
- `alembic upgrade head` applies the latest database schema before running integration flows.

## Coding Style & Naming Conventions
- Follow Ruff’s 4-space indentation, 100-character line limit, and double-quote preference.
- Modules use snake_case file names; classes use PascalCase, functions and instances snake_case.
- Group imports as `standard library`, `third-party`, then `app.*` modules; rely on `ruff --fix` for ordering.
- Avoid inline comments for clarity; prefer descriptive variable and function names.

## Testing Guidelines
- Write pytest cases in files named `test_*.py`, placing fixtures in `tests/conftest.py` or dedicated `fixtures/` modules.
- Mirror the `app/` hierarchy so each feature ships with matching unit and service tests.
- Aim for >85% coverage on new code; highlight lower coverage in the PR description with rationale.
- Use Celery’s `app.core.celery` testing utilities and test doubles under `tests/mocks/` when background tasks are involved.

## Commit & Pull Request Guidelines
- Follow the conventional style seen in history: `type(scope): summary`, with `type` in English (`feat`, `fix`, `refactor`, `docs`, `chore`) and `scope` matching the touched module.
- Keep commit messages concise (≤72 characters) and use English summaries, optionally appending short Chinese clarifications if helpful.
- Pull requests must include: problem statement, solution outline, manual or automated test evidence, and links to any related issues or tickets.
- Add screenshots or CLI excerpts when the change affects user-facing responses or operational dashboards.

## Operations & Configuration Tips
- Load environment variables via `.env` for local runs; sensitive defaults belong in deployment configuration, not version control.
- Use `docker-compose.yml` for full-service development; `docker-compose master-slave` is reserved for sharded database testing.
- Schedule Celery beat and worker locally with `./run_beat.sh` and `./run_worker.sh`; ensure Redis is running from `redis/` configuration before starting either process.
