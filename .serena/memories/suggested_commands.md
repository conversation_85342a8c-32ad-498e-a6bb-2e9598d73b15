# Suggested Commands
- Run API locally: `uvicorn app.main:app --host 0.0.0.0 --port 8000`.
- Lint code: `ruff check .` ; auto-fix/format: `ruff check --fix .` and `ruff format .`.
- Run tests: `pytest` (tests ignored by Ruff but still executed via pytest).
- Database migrations: `alembic upgrade head` (generate via `alembic revision --autogenerate -m "msg"`).
- Celery worker/beat: `./run_worker.sh` and `./run_beat.sh`.
