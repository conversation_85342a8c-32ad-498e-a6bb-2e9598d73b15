# Code Style and Conventions
- Python 3.12 with strict type hints; business logic organized by domain (api endpoints, services, models, schemas).
- Formatting and linting enforced via Ruff (line length 100, quote style double); follow Pydantic v2 patterns (`model_validator`, `model_serializer`).
- Prefer descriptive logging (Loguru) and FastAPI dependency injection; keep comments minimal and focused on non-obvious logic.
