# Steam Aggregation Backend Overview
- FastAPI-based backend providing aggregated Steam content (articles, videos, scratch projects, user content) with REST APIs, background tasks, and media storage via OSS/CDN.
- Relies on PostgreSQL (SQLAlchemy ORM + Alembic), Redis (FastAPI cache + rate limiting), and Celery with gevent workers for async jobs.
- Uses Pydantic v2 schemas, modular app layout (`app/api`, `app/models`, `app/services`, etc.), and <PERSON><PERSON> compose configs for local infrastructure.
