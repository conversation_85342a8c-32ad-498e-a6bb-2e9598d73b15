"""文件上传相关的API接口"""

import asyncio
import os
import shutil
import uuid
from enum import Enum

from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    HTTPException,
    Query,
    Request,
    UploadFile,
)
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.config import get_settings
from app.crud.file_hash import file_hash as crud_file_hash
from app.db.redis import set_key
from app.db.session import get_db
from app.schemas.file_hash import FileHashCreate
from app.schemas.upload import UploadStatus
from app.services.service_factory import get_task_status_service, get_video_folder_service
from app.services.task_status_service import TaskStatusService
from app.services.video_folder_service import VideoFolderService
from app.tasks.image_processing import (
    link_video_to_existing_file,
    process_batch_video_and_update_db,
    process_image_task,
)

settings = get_settings()
router = APIRouter()

# --- Constants and Helpers ---
MAX_FILE_SIZE = 5 * 1024 * 1024 * 1024  # 5GB
TEMP_UPLOAD_DIR = "/tmp/uploads"
os.makedirs(TEMP_UPLOAD_DIR, exist_ok=True)


async def check_file_size(request: Request):
    """依赖项，用于检查上传文件的大小"""
    content_length = request.headers.get("content-length")
    if not content_length:
        raise HTTPException(status_code=411, detail="Content-Length header required")
    if int(content_length) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=413, detail=f"File is too large. Limit is {MAX_FILE_SIZE} bytes."
        )


async def calculate_file_hash_from_path(file_path: str) -> str:
    """从文件路径流式计算哈希值"""
    import hashlib

    CHUNK_SIZE = 8 * 1024 * 1024  # 8MB
    hash_algorithm = hashlib.sha256()
    with open(file_path, "rb") as f:
        while chunk := f.read(CHUNK_SIZE):
            hash_algorithm.update(chunk)
            await asyncio.sleep(0)
    return hash_algorithm.hexdigest()


class ContentType(str, Enum):
    """上传文件的内容类型"""

    IMAGE = "image"
    VIDEO = "video"


class VideoQuality(str, Enum):
    """视频质量选项"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class UploadResponse(BaseModel):
    """上传响应"""

    file_hash: str
    file_url: str | None = None
    duration: int | None = None
    width: int | None = None
    height: int | None = None


async def handle_single_image_upload(
    file: UploadFile,
    db: AsyncSession,
) -> UploadResponse:
    """Process a single image upload and persist metadata via the file hash flow."""
    temp_file_path = os.path.join(TEMP_UPLOAD_DIR, f"{uuid.uuid4()}_{file.filename}")
    try:
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
    finally:
        file.file.close()

    file_hash = await calculate_file_hash_from_path(temp_file_path)
    cache_key = f"file_hash:{file_hash}"

    if db_file_hash := await crud_file_hash.get_by_hash(db, file_hash=file_hash):
        await set_key(cache_key, db_file_hash.file_path, expire=settings.FILE_HASH_EXPIRE)
        os.remove(temp_file_path)
        return UploadResponse(
            file_hash=file_hash,
            file_url=db_file_hash.file_path,
            **(db_file_hash.file_metadata or {}),
        )

    file_metadata: dict[str, int] = {}
    with open(temp_file_path, "rb") as f:
        file_data = f.read()

    file_url = process_image_task.delay(file_data, file_hash).get(timeout=60)
    if not file_url:
        os.remove(temp_file_path)
        raise HTTPException(status_code=500, detail="Failed to process image")

    file_hash_in = FileHashCreate(
        file_path=file_url, file_hash=file_hash, file_metadata=file_metadata
    )
    await crud_file_hash.create(db, obj_in=file_hash_in)
    await set_key(cache_key, file_url, expire=settings.FILE_HASH_EXPIRE)

    os.remove(temp_file_path)
    return UploadResponse(file_hash=file_hash, file_url=file_url, **file_metadata)

@router.post("/file", response_model=UploadResponse, dependencies=[Depends(check_file_size)])
async def upload_file(
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
) -> UploadResponse:
    """(V3 - Streaming) 上传文件（仅图片），并将处理后的元数据存入file_hash表"""
    return await handle_single_image_upload(file=file, db=db)


@router.post(
    "/batch",
    response_model=schemas.BatchUploadResponse,
    dependencies=[Depends(check_file_size)],
)
async def upload_batch_files(
    files: list[UploadFile] = File(...),
    folder_id: int = Query(..., description="目标文件夹ID"),
    video_quality: VideoQuality = Form(VideoQuality.MEDIUM),
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    task_status_service: TaskStatusService = Depends(get_task_status_service),
    video_folder_service: VideoFolderService = Depends(get_video_folder_service),
):
    """(Ultimate) 批量上传视频文件，支持流式处理、秒传和精细化状态"""
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")
    if not folder_id:
        raise HTTPException(status_code=400, detail="Folder ID is required")
    # 检查文件夹是否存在且属于当前用户
    folder = await crud.video_folder.get(db, id=folder_id)
    if not folder or folder.user_id != current_user.id:
        raise HTTPException(status_code=400, detail="Invalid folder ID")

    tasks_info = []
    files_to_process = []

    for file in files:
        if file.filename:
            video_in = schemas.VideoCreate(
                title=file.filename,
                folder_id=folder_id,
            )
            # 使用服务层创建视频记录
            video_obj = await video_folder_service.create_video_record_for_upload(
                db=db, video_in=video_in, author_id=current_user.id
            )
            files_to_process.append({"file": file, "video_obj": video_obj})

    if not files_to_process:
        raise HTTPException(status_code=400, detail="No valid files to process")

    for item in files_to_process:
        file = item["file"]
        video_obj = item["video_obj"]

        # 流式保存到临时文件
        temp_file_path = os.path.join(TEMP_UPLOAD_DIR, f"{video_obj.id}_{file.filename}")
        try:
            with open(temp_file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
        finally:
            file.file.close()

        # 使用原始文件哈希进行秒传检查
        original_file_hash = await calculate_file_hash_from_path(temp_file_path)
        existing_file = await crud_file_hash.get_by_hash(db, file_hash=original_file_hash)

        if existing_file and existing_file.file_metadata:
            task = link_video_to_existing_file.delay(
                video_id=video_obj.id, existing_file_hash_id=existing_file.id
            )
            os.remove(temp_file_path)  # 秒传成功，直接删除临时文件
        else:
            task = process_batch_video_and_update_db.delay(
                video_id=video_obj.id,
                temp_file_path=temp_file_path,
                original_file_hash=original_file_hash,
                original_filename=file.filename,
                video_quality=video_quality.value,
            )

        await task_status_service.update_status(
            task.id, status=UploadStatus.UPLOADING, progress=0.05
        )
        tasks_info.append(schemas.TaskInfo(fileName=file.filename, taskId=task.id))

    return schemas.BatchUploadResponse(data=schemas.BatchUploadResponseData(tasks=tasks_info))


@router.post("/status/batch", response_model=schemas.BatchStatusResponse)
async def get_batch_upload_status(
    status_request: schemas.BatchStatusRequest,
    task_status_service: TaskStatusService = Depends(get_task_status_service),
):
    """(Ultimate) 批量查询上传任务的状态"""
    if not status_request.task_ids:
        return schemas.BatchStatusResponse(tasks=[])
    statuses = await task_status_service.get_statuses(status_request.task_ids)
    return schemas.BatchStatusResponse(tasks=statuses)
