import base64
import logging
from datetime import datetime
from enum import Enum
from typing import Any
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator, model_serializer, model_validator
from app.config import get_settings

logger = logging.getLogger(__name__)

settings = get_settings()
CDN_DOMAIN = settings.OSS_CDN_DOMAIN

class BackpackItemType(str, Enum):
    """背包项目类型枚举"""

    COSTUME = "costume"
    SOUND = "sound"
    SPRITE = "sprite"
    SCRIPT = "script"


class BackpackItemBase(BaseModel):
    """背包项目基础模型"""

    type: BackpackItemType = Field(..., description="项目类型")
    mime: str = Field(..., description="资源MIME类型")
    name: str = Field(..., max_length=255, description="用户自定义名称")

    model_config = ConfigDict(from_attributes=True)


class BackpackItemCreate(BackpackItemBase):
    """创建背包项目的请求模型"""

    body: str = Field(..., description="Base64编码的资源内容（无data URI前缀）")
    thumbnail: str = Field(..., description="Base64编码的缩略图内容（无data URI前缀）")

    @field_validator("body", "thumbnail")
    @classmethod
    def validate_base64(cls, v: str) -> str:
        """验证Base64编码"""
        if not v:
            raise ValueError("Base64内容不能为空")

        try:
            # 尝试解码Base64
            decoded = base64.b64decode(v)
            if len(decoded) == 0:
                raise ValueError("Base64解码后内容为空")
        except Exception as e:
            raise ValueError(f"无效的Base64编码: {str(e)}")

        return v

    @model_validator(mode="after")
    def validate_type_mime_compatibility(self) -> "BackpackItemCreate":
        """验证类型与MIME类型的兼容性"""
        type_mime_map = {
            BackpackItemType.COSTUME: ["image/svg+xml", "image/png", "image/jpeg", "image/gif"],
            BackpackItemType.SOUND: ["audio/wav", "audio/mp3", "audio/mpeg", "audio/ogg"],
            BackpackItemType.SPRITE: ["application/zip", "application/octet-stream"],
            BackpackItemType.SCRIPT: ["application/json", "text/json"],
        }

        allowed_mimes = type_mime_map.get(self.type, [])
        if self.mime not in allowed_mimes:
            raise ValueError(f"类型 {self.type} 不支持MIME类型 {self.mime}")

        return self


class BackpackItemResponse(BackpackItemBase):
    """背包项目响应模型"""

    id: str = Field(..., description="项目唯一标识")
    thumbnail: str = Field(..., description="缩略图相对路径")
    body: str = Field(..., description="资源文件相对路径")
    createdAt: datetime = Field(..., description="创建时间", alias="created_at")
    size: int = Field(..., description="文件大小（字节）")

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        # 字段别名映射
        alias_generator=lambda field_name: {"created_at": "createdAt"}.get(field_name, field_name),
    )

    @model_validator(mode="before")
    @classmethod
    def convert_uuid_to_string(cls, values: Any) -> Any:
        """将UUID转换为字符串"""
        if isinstance(values, dict):
            if "id" in values and isinstance(values["id"], UUID):
                values["id"] = str(values["id"])
        elif hasattr(values, "id") and isinstance(values.id, UUID) and hasattr(values, "__table__"):
            # 处理ORM对象，同时保留计算属性
            values_dict = {col.name: getattr(values, col.name) for col in values.__table__.columns}
            values_dict["id"] = str(values.id)
            values_dict["thumbnail"] = values.thumbnail
            values_dict["body"] = values.body
            values_dict["size"] = values.size
            return values_dict

        return values

    @model_serializer(mode="wrap")
    def add_cdn_prefix(self, nxt):
        data = nxt(self)

        for field in ("thumbnail", "body"):
            if field in data and data[field]:
                url = data[field]
                if isinstance(url, str):
                    if url.startswith("/"):
                        data[field] = CDN_DOMAIN + url
                    elif url.startswith("steam/") and not url.startswith(("http://", "https://")):
                        data[field] = CDN_DOMAIN + "/" + url

        return data


class BackpackItemUpdate(BaseModel):
    """更新背包项目的请求模型"""

    name: str | None = Field(None, max_length=255, description="用户自定义名称")
    extra_data: dict[str, Any] | None = Field(None, description="元数据")


class BackpackItemList(BaseModel):
    """背包项目列表响应模型"""

    items: list[BackpackItemResponse] = Field(..., description="背包项目列表")
    total: int = Field(..., description="总数量")
    limit: int = Field(..., description="每页限制")
    offset: int = Field(..., description="偏移量")
    has_more: bool = Field(..., description="是否还有更多数据")


class BackpackItemQuery(BaseModel):
    """背包项目查询参数模型"""

    limit: int = Field(20, ge=1, le=50, description="每页数量，默认20，最大50")
    offset: int = Field(0, ge=0, description="偏移量，默认0")
    type: BackpackItemType | None = Field(None, description="按类型筛选")

    @field_validator("limit")
    @classmethod
    def validate_limit(cls, v: int) -> int:
        """验证limit参数"""
        if v < 1:
            return 1
        if v > 50:
            return 50
        return v


class BackpackStorageInfo(BaseModel):
    """背包存储信息模型"""

    total_items: int = Field(..., description="总项目数")
    total_size: int = Field(..., description="总大小（字节）")
    used_quota: float = Field(..., description="已使用配额百分比")
    max_quota: int = Field(..., description="最大配额（字节）")


class BackpackItemStats(BaseModel):
    """背包项目统计信息"""

    costume_count: int = Field(0, description="造型数量")
    sound_count: int = Field(0, description="声音数量")
    sprite_count: int = Field(0, description="精灵数量")
    script_count: int = Field(0, description="脚本数量")
    total_count: int = Field(0, description="总数量")
    total_size: int = Field(0, description="总大小（字节）")
