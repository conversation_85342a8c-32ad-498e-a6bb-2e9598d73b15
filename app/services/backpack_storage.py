"""
背包存储服务
处理文件上传、存储和管理 - 使用阿里云OSS，支持文件去重
"""

import base64
import hashlib
import logging
import uuid
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.config import get_settings
from app.crud.file_hash import file_hash as crud_file_hash
from app.db.redis import get_key, set_key
from app.models.backpack import BackpackItemType
from app.schemas.file_hash import FileHashCreate
from app.services.partial_upload import PartialUpload

import asyncio

from app.tasks.example import upload_backpack_object

logger = logging.getLogger(__name__)
settings = get_settings()


class BackpackStorageService:
    """背包存储服务类 - 使用阿里云OSS，支持文件去重"""

    def __init__(self):
        self.max_file_size = settings.MAX_FILE_SIZE  # 使用配置中的文件大小限制
        self.partial_upload = PartialUpload()  # 复用现有的上传服务

    def _calculate_file_hash(self, data: bytes) -> str:
        """计算文件的SHA256哈希值"""
        return hashlib.sha256(data).hexdigest()

    def _get_file_extension(self, mime_type: str, item_type: BackpackItemType) -> str:
        """根据MIME类型和项目类型获取文件扩展名"""
        mime_to_ext = {
            # 图片类型
            "image/svg+xml": ".svg",
            "image/png": ".png",
            "image/jpeg": ".jpg",
            "image/gif": ".gif",
            # 音频类型
            "audio/wav": ".wav",
            "audio/mp3": ".mp3",
            "audio/mpeg": ".mp3",
            "audio/ogg": ".ogg",
            # 其他类型
            "application/zip": ".zip",
            "application/octet-stream": ".dat",
            "application/json": ".json",
            "text/json": ".json",
        }

        # 对于sprite类型，强制使用.dat扩展名
        if item_type == BackpackItemType.SPRITE:
            return ".dat"

        return mime_to_ext.get(mime_type, ".dat")

    def _validate_file_size(self, data: bytes) -> None:
        """验证文件大小"""
        if len(data) > self.max_file_size:
            raise ValueError(f"文件大小超过限制 {self.max_file_size} 字节")

    async def _upload_via_celery(self, data: bytes, object_name: str) -> str:
        """Schedule OSS upload via Celery and return stored path."""

        encoded_payload = base64.b64encode(data).decode("ascii")
        timeout = getattr(settings, "BACKPACK_UPLOAD_TIMEOUT", 120)
        loop = asyncio.get_running_loop()

        def _dispatch_upload() -> str:
            async_result = upload_backpack_object.delay(encoded_payload, object_name)
            return async_result.get(timeout=timeout)

        try:
            file_url = await loop.run_in_executor(None, _dispatch_upload)
        except Exception as exc:  # pragma: no cover - Celery failure surface
            logger.error("使用 Celery 上传背包文件失败: %s", exc)
            raise ValueError(f"文件上传失败: {exc}") from exc

        if not file_url:
            raise ValueError("Celery 上传返回结果为空")

        return file_url

    async def _save_file_with_dedup(
        self, data: bytes, filename: str, subfolder: str, db: AsyncSession
    ) -> str:
        """保存文件到OSS，支持去重"""
        # 计算文件哈希
        file_hash = self._calculate_file_hash(data)
        cache_key = f"backpack_file_hash:{file_hash}"

        # 检查Redis缓存
        cached_path = await get_key(cache_key)
        if cached_path:
            logger.info(f"文件去重命中缓存: {file_hash} -> {cached_path}")
            return cached_path

        # 检查数据库中是否已存在
        existing_file = await crud_file_hash.get_by_hash(db, file_hash=file_hash)
        if existing_file:
            # 缓存到Redis
            await set_key(cache_key, existing_file.file_path, expire=settings.FILE_HASH_EXPIRE)
            logger.info(f"文件去重命中数据库: {file_hash} -> {existing_file.file_path}")
            return existing_file.file_path

        # 文件不存在，需要上传
        # 构建完整的对象路径
        object_name = f"steam/backpack/{subfolder}/{filename}"

        try:
            file_url = await self._upload_via_celery(data, object_name)

            # 如果上传任务返回的是相对路径，转换为纯object_name
            if file_url.startswith("/"):
                file_url = file_url[1:]

            # 保存到数据库
            file_hash_obj = FileHashCreate(
                file_path=file_url,
                file_hash=file_hash,
                file_metadata={"subfolder": subfolder, "original_filename": filename},
            )
            await crud_file_hash.create(db, obj_in=file_hash_obj)

            # 缓存到Redis
            await set_key(cache_key, file_url, expire=settings.FILE_HASH_EXPIRE)

            logger.info(f"文件上传成功: {file_hash} -> {file_url}")
            return file_url

        except Exception as e:
            logger.error(f"文件上传失败: {file_hash}, 错误: {str(e)}")
            raise ValueError(f"文件上传失败: {str(e)}") from e

    async def save_backpack_item(
        self,
        username: str,
        item_type: BackpackItemType,
        mime_type: str,
        body_base64: str,
        thumbnail_base64: str,
        db: AsyncSession,
    ) -> tuple[str, str, int]:
        """
        保存背包项目文件

        Args:
            username: 用户名
            item_type: 项目类型
            mime_type: MIME类型
            body_base64: 主体文件的Base64编码
            thumbnail_base64: 缩略图的Base64编码

        Returns:
            Tuple[str, str, int]: (body_path, thumbnail_path, body_size)
        """
        try:
            # 解码Base64数据
            body_data = base64.b64decode(body_base64)
            thumbnail_data = base64.b64decode(thumbnail_base64)

            # 验证文件大小
            self._validate_file_size(body_data)
            self._validate_file_size(thumbnail_data)

            # 生成唯一文件名
            file_id = str(uuid.uuid4())
            timestamp = datetime.now().strftime("%Y%m%d")

            # 主体文件 - 使用去重上传
            body_ext = self._get_file_extension(mime_type, item_type)
            body_filename = f"{username}_{timestamp}_{file_id}{body_ext}"
            body_path = await self._save_file_with_dedup(body_data, body_filename, "objects", db)

            # 缩略图文件（统一使用.jpg）- 使用去重上传
            thumbnail_filename = f"{username}_{timestamp}_{file_id}.jpg"
            thumbnail_path = await self._save_file_with_dedup(
                thumbnail_data, thumbnail_filename, "thumbnails", db
            )

            logger.info(f"保存背包项目文件成功: {body_path}, {thumbnail_path}")

            return body_path, thumbnail_path, len(body_data)

        except Exception as e:
            logger.error(f"保存背包项目文件失败: {str(e)}")
            raise

    async def delete_backpack_item_files(
        self, body_path: str, thumbnail_path: str, db: AsyncSession
    ) -> None:
        """删除背包项目文件 - 支持引用计数，只有当文件不被其他项目使用时才真正删除"""
        try:
            # 检查主体文件是否还被其他项目使用
            if body_path:
                await self._try_delete_file_if_unused(body_path, db)

            # 检查缩略图文件是否还被其他项目使用
            if thumbnail_path:
                await self._try_delete_file_if_unused(thumbnail_path, db)

        except Exception as e:
            logger.error(f"删除背包项目文件失败: {str(e)}")
            # 不抛出异常，避免影响数据库删除操作

    async def _try_delete_file_if_unused(self, file_path: str, db: AsyncSession) -> None:
        """如果文件不被其他项目使用，则删除文件"""
        try:
            # 检查是否还有其他记录使用这个文件
            from sqlalchemy import func, select

            from app.models.backpack import BackpackItem

            # 检查背包项目中是否还有使用这个文件的记录
            query = select(func.count(BackpackItem.id)).where(
                (BackpackItem.body_path == file_path) | (BackpackItem.thumbnail_path == file_path)
            )
            result = await db.execute(query)
            usage_count = result.scalar()

            if usage_count == 0:
                # 没有其他项目使用这个文件，可以安全删除
                try:
                    self.partial_upload.bucket.delete_object(file_path)
                    logger.info(f"从OSS删除未使用文件成功: {file_path}")

                    # 同时从file_hash表中删除记录
                    file_hash_record = await crud_file_hash.get_by_hash(db, file_hash=file_path)
                    if file_hash_record:
                        await crud_file_hash.remove(db, id=file_hash_record.id)

                except Exception as e:
                    logger.warning(f"从OSS删除文件失败: {file_path}, 错误: {str(e)}")
            else:
                logger.info(f"文件仍被 {usage_count} 个项目使用，跳过删除: {file_path}")

        except Exception as e:
            logger.warning(f"检查文件使用情况失败: {file_path}, 错误: {str(e)}")

    def get_file_url(self, file_path: str) -> str:
        """获取文件的访问URL"""
        if not file_path:
            return ""

        # 如果已经是完整URL，直接返回
        if file_path.startswith(("http://", "https://")):
            return file_path

        # 拼接CDN域名
        cdn_domain = getattr(settings, "OSS_CDN_DOMAIN", "")
        if cdn_domain:
            return f"{cdn_domain.rstrip('/')}/{file_path.lstrip('/')}"

        # 如果没有CDN域名，返回相对路径
        return f"/{file_path.lstrip('/')}"

    def validate_mime_type(self, mime_type: str, item_type: BackpackItemType) -> bool:
        """验证MIME类型是否与项目类型匹配"""
        type_mime_map = {
            BackpackItemType.COSTUME: ["image/svg+xml", "image/png", "image/jpeg", "image/gif"],
            BackpackItemType.SOUND: ["audio/wav", "audio/mp3", "audio/mpeg", "audio/ogg"],
            BackpackItemType.SPRITE: ["application/zip", "application/octet-stream"],
            BackpackItemType.SCRIPT: ["application/json", "text/json"],
        }

        allowed_mimes = type_mime_map.get(item_type, [])
        return mime_type in allowed_mimes


# 创建全局实例
backpack_storage = BackpackStorageService()
